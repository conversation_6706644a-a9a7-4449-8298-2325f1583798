#!/usr/bin/env node

/**
 * Jazz Framework Integration Test Script
 * 
 * This script tests the Jazz framework integration with ReelMind.next
 * to ensure all components are properly integrated and functional.
 */

const fs = require('fs');
const path = require('path');

console.log('🎵 Jazz Framework Integration Test\n');

// Test configuration
const tests = [
  {
    name: 'Sidebar Integration',
    description: 'Check if Nolan Test is added to sidebar',
    test: () => {
      const sidebarPath = path.join(__dirname, '../components/layout/sidebar.tsx');
      if (!fs.existsSync(sidebarPath)) {
        return { success: false, message: 'Sidebar file not found' };
      }
      
      const sidebarContent = fs.readFileSync(sidebarPath, 'utf8');
      const hasNolanTest = sidebarContent.includes('nolan-test') && sidebarContent.includes('Nolan Test');
      
      return {
        success: hasNolanTest,
        message: hasNolanTest ? 'Nolan Test found in sidebar' : 'Nolan Test not found in sidebar'
      };
    }
  },
  
  {
    name: 'Page Structure',
    description: 'Verify all Jazz pages exist',
    test: () => {
      const pages = [
        'app/agents/nolan-test/page.tsx',
        'app/agents/nolan-test/chat/page.tsx',
        'app/agents/nolan-test/canvas/page.tsx',
        'app/agents/nolan-test/knowledge/page.tsx',
        'app/agents/nolan-test/studio/page.tsx'
      ];
      
      const missing = [];
      const existing = [];
      
      pages.forEach(page => {
        const pagePath = path.join(__dirname, '..', page);
        if (fs.existsSync(pagePath)) {
          existing.push(page);
        } else {
          missing.push(page);
        }
      });
      
      return {
        success: missing.length === 0,
        message: missing.length === 0 
          ? `All ${existing.length} pages exist`
          : `Missing pages: ${missing.join(', ')}`
      };
    }
  },
  
  {
    name: 'Jazz Components',
    description: 'Check if Jazz components are created',
    test: () => {
      const components = [
        'components/jazz/components/JazzChatInterface.tsx',
        'components/jazz/components/JazzCanvasStudio.tsx',
        'components/jazz/components/JazzKnowledgeBase.tsx',
        'components/jazz/adapters/index.ts'
      ];
      
      const missing = [];
      const existing = [];
      
      components.forEach(component => {
        const componentPath = path.join(__dirname, '..', component);
        if (fs.existsSync(componentPath)) {
          existing.push(component);
        } else {
          missing.push(component);
        }
      });
      
      return {
        success: missing.length === 0,
        message: missing.length === 0 
          ? `All ${existing.length} components exist`
          : `Missing components: ${missing.join(', ')}`
      };
    }
  },
  
  {
    name: 'Layout Integration',
    description: 'Verify Jazz layout file exists',
    test: () => {
      const layoutPath = path.join(__dirname, '../app/agents/nolan-test/layout.tsx');
      const exists = fs.existsSync(layoutPath);
      
      if (exists) {
        const content = fs.readFileSync(layoutPath, 'utf8');
        const hasJazzStyles = content.includes('jazz-container') && content.includes('Jazz Framework');
        
        return {
          success: hasJazzStyles,
          message: hasJazzStyles ? 'Layout with Jazz styles found' : 'Layout exists but missing Jazz styles'
        };
      }
      
      return {
        success: false,
        message: 'Layout file not found'
      };
    }
  },
  
  {
    name: 'Component Imports',
    description: 'Check if pages import Jazz components correctly',
    test: () => {
      const pageChecks = [
        {
          file: 'app/agents/nolan-test/chat/page.tsx',
          import: 'JazzChatInterface'
        },
        {
          file: 'app/agents/nolan-test/canvas/page.tsx',
          import: 'JazzCanvasStudio'
        },
        {
          file: 'app/agents/nolan-test/knowledge/page.tsx',
          import: 'JazzKnowledgeBase'
        }
      ];
      
      const results = [];
      
      pageChecks.forEach(check => {
        const filePath = path.join(__dirname, '..', check.file);
        if (fs.existsSync(filePath)) {
          const content = fs.readFileSync(filePath, 'utf8');
          const hasImport = content.includes(check.import);
          results.push({
            file: check.file,
            hasImport,
            component: check.import
          });
        }
      });
      
      const successful = results.filter(r => r.hasImport).length;
      const total = results.length;
      
      return {
        success: successful === total,
        message: `${successful}/${total} pages have correct imports`
      };
    }
  },
  
  {
    name: 'Documentation',
    description: 'Check if integration documentation exists',
    test: () => {
      const docPath = path.join(__dirname, '../JAZZ_INTEGRATION.md');
      const exists = fs.existsSync(docPath);
      
      if (exists) {
        const content = fs.readFileSync(docPath, 'utf8');
        const hasContent = content.length > 1000 && content.includes('Jazz Framework Integration');
        
        return {
          success: hasContent,
          message: hasContent ? 'Complete documentation found' : 'Documentation exists but incomplete'
        };
      }
      
      return {
        success: false,
        message: 'Documentation file not found'
      };
    }
  }
];

// Run tests
let passedTests = 0;
let totalTests = tests.length;

console.log('Running integration tests...\n');

tests.forEach((test, index) => {
  process.stdout.write(`${index + 1}. ${test.name}: `);
  
  try {
    const result = test.test();
    
    if (result.success) {
      console.log(`✅ ${result.message}`);
      passedTests++;
    } else {
      console.log(`❌ ${result.message}`);
    }
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }
});

// Summary
console.log('\n' + '='.repeat(50));
console.log(`Test Results: ${passedTests}/${totalTests} tests passed`);

if (passedTests === totalTests) {
  console.log('🎉 All tests passed! Jazz integration is successful.');
  console.log('\nNext steps:');
  console.log('1. Start the ReelMind Python backend server');
  console.log('2. Run `npm run dev` to start the Next.js development server');
  console.log('3. Navigate to /agents/nolan-test to access Jazz features');
} else {
  console.log('⚠️  Some tests failed. Please review the integration.');
  console.log('\nTo fix issues:');
  console.log('1. Check file paths and imports');
  console.log('2. Ensure all components are properly created');
  console.log('3. Verify sidebar integration');
}

console.log('\n🎵 Jazz Framework Integration Test Complete');

// Exit with appropriate code
process.exit(passedTests === totalTests ? 0 : 1);
