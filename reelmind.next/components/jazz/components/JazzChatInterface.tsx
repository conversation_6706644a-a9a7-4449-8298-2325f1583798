"use client";

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Send, Bot, User, Loader2, <PERSON><PERSON><PERSON>, Copy, ThumbsUp, ThumbsDown } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { jazzAPI } from '../adapters';

interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  status?: 'sending' | 'sent' | 'error';
  metadata?: {
    model?: string;
    tokens?: number;
    processingTime?: number;
  };
}

interface JazzChatInterfaceProps {
  className?: string;
  onMessage?: (message: Message) => void;
  initialMessages?: Message[];
  placeholder?: string;
  showMetadata?: boolean;
}

export function JazzChatInterface({
  className = '',
  onMessage,
  initialMessages = [],
  placeholder = "Type your message here...",
  showMetadata = false
}: JazzChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>(initialMessages);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(true);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [inputValue]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      content: inputValue.trim(),
      role: 'user',
      timestamp: new Date(),
      status: 'sending'
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      // Update message status to sent
      setMessages(prev => prev.map(msg => 
        msg.id === userMessage.id ? { ...msg, status: 'sent' } : msg
      ));

      // Call Jazz API adapter to send message to ReelMind backend
      const startTime = Date.now();
      const response = await jazzAPI.sendMessage({
        message: userMessage.content,
        conversation_id: 'jazz-chat-session',
        model: 'gpt-4'
      });

      const processingTime = Date.now() - startTime;

      const assistantMessage: Message = {
        id: `assistant-${Date.now()}`,
        content: response.content || "I'm sorry, I couldn't process your request at the moment.",
        role: 'assistant',
        timestamp: new Date(),
        status: 'sent',
        metadata: {
          model: response.model || 'gpt-4',
          tokens: response.tokens || 0,
          processingTime
        }
      };

      setMessages(prev => [...prev, assistantMessage]);
      
      if (onMessage) {
        onMessage(assistantMessage);
      }

    } catch (error) {
      console.error('Error sending message:', error);
      
      // Update user message status to error
      setMessages(prev => prev.map(msg => 
        msg.id === userMessage.id ? { ...msg, status: 'error' } : msg
      ));

      // Add error message
      const errorMessage: Message = {
        id: `error-${Date.now()}`,
        content: "Sorry, I encountered an error while processing your message. Please try again.",
        role: 'assistant',
        timestamp: new Date(),
        status: 'error'
      };

      setMessages(prev => [...prev, errorMessage]);
      setIsConnected(false);
      
      // Reconnect after 3 seconds
      setTimeout(() => setIsConnected(true), 3000);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
  };

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className={`jazz-chat flex flex-col h-full ${className}`}>
      {/* Header */}
      <Card className="flex-shrink-0">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Bot className="h-5 w-5 text-primary" />
              <span>Jazz AI Assistant</span>
              <Badge variant={isConnected ? "default" : "destructive"} className="ml-2">
                {isConnected ? "Connected" : "Disconnected"}
              </Badge>
            </CardTitle>
            <Button variant="ghost" size="sm">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
          {showMetadata && (
            <div className="text-sm text-muted-foreground">
              Backend: ReelMind Python Server • Model: GPT-4
            </div>
          )}
        </CardHeader>
      </Card>

      {/* Messages Area */}
      <Card className="flex-1 flex flex-col min-h-0 mt-4">
        <CardContent className="flex-1 p-0">
          <ScrollArea className="h-full jazz-scrollbar" ref={scrollAreaRef}>
            <div className="p-4 space-y-4">
              <AnimatePresence>
                {messages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                    className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[80%] group relative ${
                        message.role === 'user'
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted'
                      } chat-message`}
                    >
                      {/* Message Header */}
                      <div className="flex items-center space-x-2 mb-1">
                        {message.role === 'assistant' ? (
                          <Bot className="h-3 w-3" />
                        ) : (
                          <User className="h-3 w-3" />
                        )}
                        <span className="text-xs font-medium">
                          {message.role === 'assistant' ? 'Jazz AI' : 'You'}
                        </span>
                        <span className="text-xs opacity-70">
                          {formatTimestamp(message.timestamp)}
                        </span>
                        {message.status === 'sending' && (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        )}
                        {message.status === 'error' && (
                          <div className="w-2 h-2 bg-red-500 rounded-full" />
                        )}
                      </div>

                      {/* Message Content */}
                      <div className="text-sm whitespace-pre-wrap">
                        {message.content}
                      </div>

                      {/* Message Metadata */}
                      {showMetadata && message.metadata && (
                        <div className="text-xs opacity-70 mt-2 pt-2 border-t border-current/20">
                          Model: {message.metadata.model} • 
                          {message.metadata.tokens && ` Tokens: ${message.metadata.tokens} • `}
                          {message.metadata.processingTime && ` ${message.metadata.processingTime}ms`}
                        </div>
                      )}

                      {/* Message Actions */}
                      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                        <div className="flex space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0"
                            onClick={() => copyMessage(message.content)}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                          {message.role === 'assistant' && (
                            <>
                              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                                <ThumbsUp className="h-3 w-3" />
                              </Button>
                              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                                <ThumbsDown className="h-3 w-3" />
                              </Button>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>

              {/* Typing Indicator */}
              {isLoading && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="flex justify-start"
                >
                  <div className="bg-muted chat-message flex items-center space-x-2">
                    <Bot className="h-3 w-3" />
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-current rounded-full animate-bounce" />
                      <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                      <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                    </div>
                  </div>
                </motion.div>
              )}
            </div>
          </ScrollArea>
        </CardContent>

        <Separator />

        {/* Input Area */}
        <div className="p-4">
          <div className="flex space-x-2">
            <div className="flex-1 relative">
              <textarea
                ref={textareaRef}
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={placeholder}
                className="w-full min-h-[44px] max-h-[120px] p-3 pr-12 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-primary jazz-focusable"
                disabled={isLoading || !isConnected}
                rows={1}
              />
              <div className="absolute bottom-2 right-2 text-xs text-muted-foreground">
                {inputValue.length}/2000
              </div>
            </div>
            <Button 
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || isLoading || !isConnected}
              className="px-4 h-[44px]"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
          
          <div className="flex justify-between items-center mt-2 text-xs text-muted-foreground">
            <span>Press Enter to send, Shift+Enter for new line</span>
            <span>{messages.length} messages</span>
          </div>
        </div>
      </Card>
    </div>
  );
}
