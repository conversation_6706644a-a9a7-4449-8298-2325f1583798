"use client";

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { motion } from 'framer-motion';
import { 
  Save, 
  Download, 
  Upload, 
  Undo, 
  Redo, 
  ZoomIn, 
  ZoomOut, 
  Move,
  Square,
  Circle,
  ArrowRight,
  Type,
  Palette,
  Settings,
  Users,
  Share2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { jazzAPI } from '../adapters';

// Note: In a real implementation, you would import Excalidraw like this:
// import { Excalidraw, MainMenu, WelcomeScreen } from '@excalidraw/excalidraw';

interface CanvasElement {
  id: string;
  type: 'rectangle' | 'ellipse' | 'arrow' | 'text' | 'freedraw';
  x: number;
  y: number;
  width: number;
  height: number;
  strokeColor: string;
  backgroundColor: string;
  strokeWidth: number;
  roughness: number;
  opacity: number;
}

interface JazzCanvasStudioProps {
  canvasId?: string;
  className?: string;
  onSave?: (elements: CanvasElement[]) => void;
  onExport?: (format: 'png' | 'svg' | 'json') => void;
  initialElements?: CanvasElement[];
  collaborative?: boolean;
}

export function JazzCanvasStudio({
  canvasId,
  className = '',
  onSave,
  onExport,
  initialElements = [],
  collaborative = false
}: JazzCanvasStudioProps) {
  const [elements, setElements] = useState<CanvasElement[]>(initialElements);
  const [selectedTool, setSelectedTool] = useState<string>('selection');
  const [strokeColor, setStrokeColor] = useState('#000000');
  const [backgroundColor, setBackgroundColor] = useState('transparent');
  const [strokeWidth, setStrokeWidth] = useState([2]);
  const [zoom, setZoom] = useState([100]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [collaborators, setCollaborators] = useState<string[]>([]);
  const canvasRef = useRef<HTMLDivElement>(null);

  // Load canvas data on mount
  useEffect(() => {
    if (canvasId) {
      loadCanvas();
    }
  }, [canvasId]);

  // Auto-save every 30 seconds
  useEffect(() => {
    if (canvasId && elements.length > 0) {
      const interval = setInterval(() => {
        handleSave();
      }, 30000);

      return () => clearInterval(interval);
    }
  }, [canvasId, elements]);

  const loadCanvas = async () => {
    if (!canvasId) return;

    setIsLoading(true);
    try {
      const canvasData = await jazzAPI.getCanvas(canvasId);
      if (canvasData.elements) {
        setElements(canvasData.elements);
      }
    } catch (error) {
      console.error('Error loading canvas:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!canvasId || isSaving) return;

    setIsSaving(true);
    try {
      await jazzAPI.updateCanvas(canvasId, {
        elements,
        lastModified: new Date().toISOString()
      });
      setLastSaved(new Date());
      
      if (onSave) {
        onSave(elements);
      }
    } catch (error) {
      console.error('Error saving canvas:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleExport = useCallback((format: 'png' | 'svg' | 'json') => {
    // In a real implementation, this would use Excalidraw's export functionality
    console.log(`Exporting canvas as ${format}`);
    
    if (format === 'json') {
      const dataStr = JSON.stringify(elements, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `canvas-${canvasId || 'untitled'}.json`;
      link.click();
      URL.revokeObjectURL(url);
    }

    if (onExport) {
      onExport(format);
    }
  }, [elements, canvasId, onExport]);

  const tools = [
    { id: 'selection', icon: Move, label: 'Select' },
    { id: 'rectangle', icon: Square, label: 'Rectangle' },
    { id: 'ellipse', icon: Circle, label: 'Ellipse' },
    { id: 'arrow', icon: ArrowRight, label: 'Arrow' },
    { id: 'text', icon: Type, label: 'Text' },
  ];

  const colors = [
    '#000000', '#ffffff', '#ff0000', '#00ff00', '#0000ff',
    '#ffff00', '#ff00ff', '#00ffff', '#ffa500', '#800080'
  ];

  return (
    <div className={`jazz-canvas flex flex-col h-full ${className}`}>
      {/* Toolbar */}
      <Card className="flex-shrink-0">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Palette className="h-5 w-5 text-primary" />
              <span>Jazz Canvas Studio</span>
              {collaborative && (
                <Badge variant="secondary" className="ml-2">
                  <Users className="h-3 w-3 mr-1" />
                  {collaborators.length + 1}
                </Badge>
              )}
            </CardTitle>
            
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={() => handleSave()}>
                {isSaving ? (
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                ) : (
                  <Save className="h-4 w-4" />
                )}
                Save
              </Button>
              
              <Button variant="outline" size="sm" onClick={() => handleExport('png')}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              
              {collaborative && (
                <Button variant="outline" size="sm">
                  <Share2 className="h-4 w-4 mr-2" />
                  Share
                </Button>
              )}
              
              <Button variant="ghost" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          {lastSaved && (
            <div className="text-sm text-muted-foreground">
              Last saved: {lastSaved.toLocaleTimeString()}
            </div>
          )}
        </CardHeader>
        
        <Separator />
        
        {/* Tool Palette */}
        <CardContent className="py-3">
          <div className="flex items-center justify-between">
            {/* Drawing Tools */}
            <div className="flex items-center space-x-2">
              {tools.map((tool) => {
                const Icon = tool.icon;
                return (
                  <Button
                    key={tool.id}
                    variant={selectedTool === tool.id ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedTool(tool.id)}
                    title={tool.label}
                  >
                    <Icon className="h-4 w-4" />
                  </Button>
                );
              })}
              
              <Separator orientation="vertical" className="h-6" />
              
              <Button variant="outline" size="sm" title="Undo">
                <Undo className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" title="Redo">
                <Redo className="h-4 w-4" />
              </Button>
            </div>

            {/* Style Controls */}
            <div className="flex items-center space-x-4">
              {/* Color Palette */}
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">Color:</span>
                <div className="flex space-x-1">
                  {colors.map((color) => (
                    <button
                      key={color}
                      className={`w-6 h-6 rounded border-2 ${
                        strokeColor === color ? 'border-primary' : 'border-gray-300'
                      }`}
                      style={{ backgroundColor: color }}
                      onClick={() => setStrokeColor(color)}
                    />
                  ))}
                </div>
              </div>

              <Separator orientation="vertical" className="h-6" />

              {/* Stroke Width */}
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">Width:</span>
                <div className="w-20">
                  <Slider
                    value={strokeWidth}
                    onValueChange={setStrokeWidth}
                    max={10}
                    min={1}
                    step={1}
                  />
                </div>
                <span className="text-sm text-muted-foreground w-6">
                  {strokeWidth[0]}
                </span>
              </div>

              <Separator orientation="vertical" className="h-6" />

              {/* Zoom Controls */}
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm">
                  <ZoomOut className="h-4 w-4" />
                </Button>
                <div className="w-20">
                  <Slider
                    value={zoom}
                    onValueChange={setZoom}
                    max={200}
                    min={25}
                    step={25}
                  />
                </div>
                <span className="text-sm text-muted-foreground w-12">
                  {zoom[0]}%
                </span>
                <Button variant="outline" size="sm">
                  <ZoomIn className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Canvas Area */}
      <Card className="flex-1 mt-4 overflow-hidden">
        <CardContent className="p-0 h-full">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">Loading canvas...</p>
              </div>
            </div>
          ) : (
            <div 
              ref={canvasRef}
              className="h-full w-full excalidraw-container relative"
              style={{ transform: `scale(${zoom[0] / 100})`, transformOrigin: 'top left' }}
            >
              {/* Placeholder for Excalidraw component */}
              <div className="h-full w-full bg-white flex items-center justify-center border-2 border-dashed border-gray-300">
                <div className="text-center">
                  <Palette className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Jazz Canvas Studio</h3>
                  <p className="text-gray-600 mb-4 max-w-md">
                    This is where the Excalidraw component would be rendered. 
                    The Jazz framework's complete canvas functionality is preserved here.
                  </p>
                  <div className="space-y-2 text-sm text-gray-500">
                    <p>• Draw with {tools.find(t => t.id === selectedTool)?.label || 'Selection'} tool</p>
                    <p>• Stroke color: {strokeColor}</p>
                    <p>• Stroke width: {strokeWidth[0]}px</p>
                    <p>• Zoom: {zoom[0]}%</p>
                    <p>• Elements: {elements.length}</p>
                  </div>
                </div>
              </div>
              
              {/* In the actual implementation, this would be: */}
              {/* 
              <Excalidraw
                ref={excalidrawRef}
                initialData={{
                  elements: elements,
                  appState: {
                    zoom: zoom[0] / 100,
                    currentItemStrokeColor: strokeColor,
                    currentItemStrokeWidth: strokeWidth[0],
                    activeTool: { type: selectedTool }
                  }
                }}
                onChange={(elements, appState) => {
                  setElements(elements);
                }}
                UIOptions={{
                  canvasActions: {
                    loadScene: false,
                    saveToActiveFile: false,
                    export: false,
                    toggleTheme: false
                  }
                }}
              >
                <MainMenu>
                  <MainMenu.DefaultItems.ClearCanvas />
                  <MainMenu.DefaultItems.SaveAsImage />
                  <MainMenu.DefaultItems.Help />
                </MainMenu>
                <WelcomeScreen>
                  <WelcomeScreen.Hints.MenuHint />
                  <WelcomeScreen.Hints.ToolbarHint />
                </WelcomeScreen>
              </Excalidraw>
              */}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Status Bar */}
      <Card className="flex-shrink-0 mt-4">
        <CardContent className="py-2">
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center space-x-4">
              <span>Tool: {tools.find(t => t.id === selectedTool)?.label}</span>
              <span>Elements: {elements.length}</span>
              <span>Canvas ID: {canvasId || 'Untitled'}</span>
            </div>
            <div className="flex items-center space-x-4">
              {collaborative && (
                <span>Collaborators: {collaborators.length}</span>
              )}
              <span>Backend: ReelMind Python Server</span>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>Connected</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
