"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Search, 
  Plus, 
  FileText, 
  Folder, 
  Edit, 
  Trash2, 
  Save,
  Eye,
  Download,
  Upload,
  Tag,
  Calendar,
  User,
  MoreHorizontal
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { jazzAPI } from '../adapters';

interface KnowledgeItem {
  id: string;
  title: string;
  content: string;
  type: 'document' | 'folder' | 'note' | 'reference';
  tags: string[];
  author: string;
  createdAt: Date;
  updatedAt: Date;
  size: number;
  parentId?: string;
  isPublic: boolean;
  version: number;
}

interface JazzKnowledgeBaseProps {
  className?: string;
  onItemSelect?: (item: KnowledgeItem) => void;
  onItemCreate?: (item: Partial<KnowledgeItem>) => void;
  onItemUpdate?: (id: string, updates: Partial<KnowledgeItem>) => void;
  onItemDelete?: (id: string) => void;
  initialItems?: KnowledgeItem[];
  searchable?: boolean;
  editable?: boolean;
}

export function JazzKnowledgeBase({
  className = '',
  onItemSelect,
  onItemCreate,
  onItemUpdate,
  onItemDelete,
  initialItems = [],
  searchable = true,
  editable = true
}: JazzKnowledgeBaseProps) {
  const [items, setItems] = useState<KnowledgeItem[]>(initialItems);
  const [selectedItem, setSelectedItem] = useState<KnowledgeItem | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState('');
  const [editTitle, setEditTitle] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');

  // Load knowledge items on mount
  useEffect(() => {
    loadKnowledgeItems();
  }, []);

  // Filter items based on search query and tags
  const filteredItems = items.filter(item => {
    const matchesSearch = !searchQuery || 
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesTags = selectedTags.length === 0 || 
      selectedTags.some(tag => item.tags.includes(tag));
    
    return matchesSearch && matchesTags;
  });

  // Get all unique tags
  const allTags = Array.from(new Set(items.flatMap(item => item.tags)));

  const loadKnowledgeItems = async () => {
    setIsLoading(true);
    try {
      const response = await jazzAPI.getKnowledgeItems();
      setItems(response.items || []);
    } catch (error) {
      console.error('Error loading knowledge items:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleItemSelect = (item: KnowledgeItem) => {
    setSelectedItem(item);
    setEditTitle(item.title);
    setEditContent(item.content);
    setIsEditing(false);
    
    if (onItemSelect) {
      onItemSelect(item);
    }
  };

  const handleCreateNew = () => {
    const newItem: KnowledgeItem = {
      id: `new-${Date.now()}`,
      title: 'New Document',
      content: '',
      type: 'document',
      tags: [],
      author: 'Current User',
      createdAt: new Date(),
      updatedAt: new Date(),
      size: 0,
      isPublic: false,
      version: 1
    };

    setItems(prev => [newItem, ...prev]);
    setSelectedItem(newItem);
    setEditTitle(newItem.title);
    setEditContent(newItem.content);
    setIsEditing(true);

    if (onItemCreate) {
      onItemCreate(newItem);
    }
  };

  const handleSave = async () => {
    if (!selectedItem) return;

    setIsSaving(true);
    try {
      const updatedItem: KnowledgeItem = {
        ...selectedItem,
        title: editTitle,
        content: editContent,
        updatedAt: new Date(),
        size: editContent.length,
        version: selectedItem.version + 1
      };

      await jazzAPI.createKnowledgeItem(updatedItem);
      
      setItems(prev => prev.map(item => 
        item.id === selectedItem.id ? updatedItem : item
      ));
      
      setSelectedItem(updatedItem);
      setIsEditing(false);

      if (onItemUpdate) {
        onItemUpdate(selectedItem.id, updatedItem);
      }
    } catch (error) {
      console.error('Error saving item:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async (itemId: string) => {
    if (!confirm('Are you sure you want to delete this item?')) return;

    try {
      setItems(prev => prev.filter(item => item.id !== itemId));
      
      if (selectedItem?.id === itemId) {
        setSelectedItem(null);
      }

      if (onItemDelete) {
        onItemDelete(itemId);
      }
    } catch (error) {
      console.error('Error deleting item:', error);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const getTypeIcon = (type: KnowledgeItem['type']) => {
    switch (type) {
      case 'folder': return Folder;
      case 'note': return Edit;
      case 'reference': return Tag;
      default: return FileText;
    }
  };

  return (
    <div className={`jazz-knowledge flex flex-col h-full ${className}`}>
      {/* Header */}
      <Card className="flex-shrink-0">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-primary" />
              <span>Jazz Knowledge Base</span>
              <Badge variant="secondary">{items.length} items</Badge>
            </CardTitle>
            
            <div className="flex items-center space-x-2">
              {editable && (
                <Button variant="outline" size="sm" onClick={handleCreateNew}>
                  <Plus className="h-4 w-4 mr-2" />
                  New Document
                </Button>
              )}
              
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Import
              </Button>
              
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        {searchable && (
          <>
            <Separator />
            <CardContent className="py-3">
              <div className="flex items-center space-x-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search knowledge base..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                {allTags.length > 0 && (
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium">Tags:</span>
                    <div className="flex flex-wrap gap-1">
                      {allTags.slice(0, 5).map(tag => (
                        <Badge
                          key={tag}
                          variant={selectedTags.includes(tag) ? "default" : "outline"}
                          className="cursor-pointer text-xs"
                          onClick={() => {
                            setSelectedTags(prev => 
                              prev.includes(tag) 
                                ? prev.filter(t => t !== tag)
                                : [...prev, tag]
                            );
                          }}
                        >
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </>
        )}
      </Card>

      {/* Main Content */}
      <div className="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-4 mt-4 min-h-0">
        {/* Items List */}
        <Card className="lg:col-span-1">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Documents</CardTitle>
              <div className="text-sm text-muted-foreground">
                {filteredItems.length} of {items.length}
              </div>
            </div>
          </CardHeader>
          
          <Separator />
          
          <CardContent className="p-0">
            <ScrollArea className="h-[calc(100vh-400px)] jazz-scrollbar">
              {isLoading ? (
                <div className="p-4 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                  <p className="text-sm text-muted-foreground">Loading...</p>
                </div>
              ) : (
                <div className="p-4 space-y-2">
                  <AnimatePresence>
                    {filteredItems.map((item) => {
                      const TypeIcon = getTypeIcon(item.type);
                      const isSelected = selectedItem?.id === item.id;
                      
                      return (
                        <motion.div
                          key={item.id}
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -10 }}
                          transition={{ duration: 0.2 }}
                          className={`knowledge-item p-3 rounded-lg cursor-pointer border transition-all ${
                            isSelected
                              ? 'bg-primary/10 border-primary/20'
                              : 'hover:bg-muted border-transparent'
                          }`}
                          onClick={() => handleItemSelect(item)}
                        >
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex items-center space-x-2 flex-1 min-w-0">
                              <TypeIcon className="h-4 w-4 text-primary flex-shrink-0" />
                              <span className="font-medium text-sm truncate">
                                {item.title}
                              </span>
                            </div>
                            
                            {editable && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDelete(item.id);
                                }}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            )}
                          </div>
                          
                          <div className="text-xs text-muted-foreground mb-2 line-clamp-2">
                            {item.content.substring(0, 100)}...
                          </div>
                          
                          <div className="flex items-center justify-between text-xs">
                            <div className="flex items-center space-x-2">
                              <Badge variant="outline" className="text-xs">
                                {item.type}
                              </Badge>
                              <span className="text-muted-foreground">
                                {formatFileSize(item.size)}
                              </span>
                            </div>
                            
                            <div className="flex items-center space-x-1 text-muted-foreground">
                              <Calendar className="h-3 w-3" />
                              <span>{item.updatedAt.toLocaleDateString()}</span>
                            </div>
                          </div>
                          
                          {item.tags.length > 0 && (
                            <div className="flex flex-wrap gap-1 mt-2">
                              {item.tags.slice(0, 3).map(tag => (
                                <Badge key={tag} variant="secondary" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </motion.div>
                      );
                    })}
                  </AnimatePresence>
                </div>
              )}
            </ScrollArea>
          </CardContent>
        </Card>

        {/* Content Editor/Viewer */}
        <Card className="lg:col-span-2">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">
                {selectedItem ? (isEditing ? 'Edit Document' : 'View Document') : 'Select a document'}
              </CardTitle>
              
              {selectedItem && editable && (
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsEditing(!isEditing)}
                  >
                    {isEditing ? <Eye className="h-4 w-4 mr-2" /> : <Edit className="h-4 w-4 mr-2" />}
                    {isEditing ? 'Preview' : 'Edit'}
                  </Button>
                  
                  {isEditing && (
                    <Button
                      variant="default"
                      size="sm"
                      onClick={handleSave}
                      disabled={isSaving}
                    >
                      {isSaving ? (
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent mr-2" />
                      ) : (
                        <Save className="h-4 w-4 mr-2" />
                      )}
                      Save
                    </Button>
                  )}
                  
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>
          </CardHeader>
          
          <Separator />
          
          <CardContent className="p-0 h-full">
            {selectedItem ? (
              <div className="h-full flex flex-col">
                {isEditing ? (
                  <div className="p-4 h-full flex flex-col space-y-4">
                    <Input
                      value={editTitle}
                      onChange={(e) => setEditTitle(e.target.value)}
                      placeholder="Document title..."
                      className="font-medium"
                    />
                    <Textarea
                      value={editContent}
                      onChange={(e) => setEditContent(e.target.value)}
                      placeholder="Start writing your document..."
                      className="flex-1 min-h-[300px] resize-none jazz-focusable"
                    />
                  </div>
                ) : (
                  <ScrollArea className="h-full jazz-scrollbar">
                    <div className="p-6">
                      <h1 className="text-2xl font-bold mb-4">{selectedItem.title}</h1>
                      
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-6">
                        <div className="flex items-center space-x-1">
                          <User className="h-4 w-4" />
                          <span>{selectedItem.author}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4" />
                          <span>Updated {selectedItem.updatedAt.toLocaleDateString()}</span>
                        </div>
                        <span>Version {selectedItem.version}</span>
                        <span>{formatFileSize(selectedItem.size)}</span>
                      </div>
                      
                      <div className="prose prose-sm max-w-none">
                        <pre className="whitespace-pre-wrap font-sans">
                          {selectedItem.content}
                        </pre>
                      </div>
                    </div>
                  </ScrollArea>
                )}
              </div>
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center text-muted-foreground">
                  <FileText className="h-16 w-16 mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-semibold mb-2">Jazz Knowledge Base</h3>
                  <p className="mb-4 max-w-md">
                    Select a document from the sidebar to view or edit it. 
                    This knowledge base integrates with the ReelMind backend for storage and search.
                  </p>
                  {editable && (
                    <Button onClick={handleCreateNew}>
                      <Plus className="h-4 w-4 mr-2" />
                      Create New Document
                    </Button>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
