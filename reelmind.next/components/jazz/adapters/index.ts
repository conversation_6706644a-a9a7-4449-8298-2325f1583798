// Jazz Framework Adapters for ReelMind.next Integration
// This file provides compatibility layers between Jazz components and Next.js

// Re-export commonly used utilities with proper paths
export { cn } from '@/lib/utils';

// API Configuration for ReelMind backend
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// Jazz API adapter - converts Jazz API calls to ReelMind backend calls
export class JazzAPIAdapter {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  // Canvas API methods
  async createCanvas(data: any) {
    const response = await fetch(`${this.baseUrl}/api/canvas`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    return response.json();
  }

  async getCanvas(id: string) {
    const response = await fetch(`${this.baseUrl}/api/canvas/${id}`);
    return response.json();
  }

  async updateCanvas(id: string, data: any) {
    const response = await fetch(`${this.baseUrl}/api/canvas/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    return response.json();
  }

  // Chat API methods
  async sendMessage(data: any) {
    const response = await fetch(`${this.baseUrl}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    return response.json();
  }

  // Knowledge API methods
  async getKnowledgeItems() {
    const response = await fetch(`${this.baseUrl}/api/knowledge`);
    return response.json();
  }

  async createKnowledgeItem(data: any) {
    const response = await fetch(`${this.baseUrl}/api/knowledge`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    return response.json();
  }

  // Agent API methods
  async getAgents() {
    const response = await fetch(`${this.baseUrl}/api/agents`);
    return response.json();
  }

  async createAgent(data: any) {
    const response = await fetch(`${this.baseUrl}/api/agents`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    return response.json();
  }
}

// Create a singleton instance
export const jazzAPI = new JazzAPIAdapter();

// Router adapter - converts TanStack Router patterns to Next.js patterns
export const routerAdapter = {
  navigate: (path: string) => {
    if (typeof window !== 'undefined') {
      window.location.href = path;
    }
  },
  
  useParams: () => {
    // This would need to be implemented based on Next.js useParams
    return {};
  },
  
  createLink: (href: string, children: React.ReactNode) => {
    // Return Next.js Link component
    return children; // Simplified for now
  }
};

// Theme adapter
export const themeAdapter = {
  useTheme: () => {
    // This would integrate with Next.js theme system
    return {
      theme: 'light',
      setTheme: (theme: string) => console.log('Setting theme:', theme)
    };
  }
};

// Socket adapter for real-time features
export class SocketAdapter {
  private socket: WebSocket | null = null;
  private url: string;

  constructor(url: string = 'ws://localhost:8000/ws') {
    this.url = url;
  }

  connect() {
    if (typeof window !== 'undefined') {
      this.socket = new WebSocket(this.url);
      
      this.socket.onopen = () => {
        console.log('Jazz Socket connected to ReelMind backend');
      };
      
      this.socket.onmessage = (event) => {
        const data = JSON.parse(event.data);
        // Handle incoming messages
        console.log('Received:', data);
      };
      
      this.socket.onclose = () => {
        console.log('Jazz Socket disconnected');
      };
    }
  }

  send(data: any) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(data));
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
  }
}

// Export singleton socket instance
export const jazzSocket = new SocketAdapter();

// Utility functions for Jazz component integration
export const jazzUtils = {
  // Convert Jazz component props to Next.js compatible props
  adaptProps: (props: any) => {
    // Remove TanStack Router specific props and adapt them for Next.js
    const { navigate, params, ...nextProps } = props;
    return nextProps;
  },

  // Handle Jazz-specific event handlers
  adaptEventHandlers: (handlers: any) => {
    return {
      ...handlers,
      onNavigate: (path: string) => {
        if (typeof window !== 'undefined') {
          window.location.href = path;
        }
      }
    };
  },

  // Convert Jazz store patterns to work with Next.js
  adaptStore: (store: any) => {
    return {
      ...store,
      // Add Next.js specific store adaptations
    };
  }
};

// Constants from Jazz project
export const JAZZ_CONSTANTS = {
  DEFAULT_SYSTEM_PROMPT: "You are a helpful AI assistant integrated with the Jazz framework and ReelMind backend.",
  API_ENDPOINTS: {
    CANVAS: '/api/canvas',
    CHAT: '/api/chat',
    KNOWLEDGE: '/api/knowledge',
    AGENTS: '/api/agents',
    UPLOAD: '/api/upload'
  }
};

// Export types for TypeScript compatibility
export interface JazzCanvasData {
  id: string;
  name: string;
  elements: any[];
  appState: any;
}

export interface JazzMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
}

export interface JazzAgent {
  id: string;
  name: string;
  type: 'chat' | 'workflow' | 'automation';
  status: 'active' | 'inactive' | 'error';
  config: any;
}
