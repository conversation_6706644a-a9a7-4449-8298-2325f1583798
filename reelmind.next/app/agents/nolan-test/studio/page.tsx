"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON>Left, 
  Cog, 
  Settings, 
  Play, 
  Pause,
  Plus,
  Bot,
  Workflow,
  Zap,
  Database,
  GitBranch
} from 'lucide-react';
import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';

interface Agent {
  id: string;
  name: string;
  type: 'chat' | 'workflow' | 'automation';
  status: 'active' | 'inactive' | 'error';
  description: string;
  lastRun?: Date;
  connections: number;
}

interface WorkflowNode {
  id: string;
  type: 'input' | 'process' | 'output' | 'condition';
  label: string;
  x: number;
  y: number;
}

export default function NolanTestStudioPage() {
  const [agents, setAgents] = useState<Agent[]>([
    {
      id: '1',
      name: 'Chat Assistant',
      type: 'chat',
      status: 'active',
      description: 'AI-powered chat assistant for user interactions',
      lastRun: new Date(),
      connections: 3
    },
    {
      id: '2',
      name: 'Image Generator',
      type: 'workflow',
      status: 'active',
      description: 'Automated image generation workflow',
      lastRun: new Date(Date.now() - 3600000),
      connections: 2
    },
    {
      id: '3',
      name: 'Data Processor',
      type: 'automation',
      status: 'inactive',
      description: 'Batch data processing and analysis',
      connections: 1
    }
  ]);

  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [isWorkflowView, setIsWorkflowView] = useState(false);

  const getStatusColor = (status: Agent['status']) => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'inactive': return 'bg-gray-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getTypeIcon = (type: Agent['type']) => {
    switch (type) {
      case 'chat': return Bot;
      case 'workflow': return Workflow;
      case 'automation': return Zap;
      default: return Cog;
    }
  };

  const handleAgentToggle = (agentId: string) => {
    setAgents(prev => prev.map(agent => 
      agent.id === agentId 
        ? { ...agent, status: agent.status === 'active' ? 'inactive' : 'active' }
        : agent
    ));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="flex items-center justify-between mb-6"
        >
          <div className="flex items-center space-x-4">
            <Link href="/agents/nolan-test">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            </Link>
            <div className="flex items-center space-x-2">
              <Cog className="h-6 w-6 text-primary" />
              <h1 className="text-2xl font-bold">Agent Studio</h1>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setIsWorkflowView(!isWorkflowView)}
            >
              <GitBranch className="h-4 w-4 mr-2" />
              {isWorkflowView ? 'List View' : 'Workflow View'}
            </Button>
            <Button variant="outline" size="sm">
              <Plus className="h-4 w-4 mr-2" />
              New Agent
            </Button>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </div>
        </motion.div>

        {/* Main Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]"
        >
          {/* Agents List */}
          <Card className="lg:col-span-1">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg">Agents</CardTitle>
              <div className="text-sm text-muted-foreground">
                {agents.filter(a => a.status === 'active').length} active, {agents.length} total
              </div>
            </CardHeader>
            
            <Separator />
            
            <CardContent className="p-0">
              <ScrollArea className="h-[calc(100vh-350px)]">
                <div className="p-4 space-y-3">
                  {agents.map((agent) => {
                    const TypeIcon = getTypeIcon(agent.type);
                    return (
                      <motion.div
                        key={agent.id}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3 }}
                        className={`p-4 rounded-lg cursor-pointer transition-colors border ${
                          selectedAgent?.id === agent.id
                            ? 'bg-primary/10 border-primary/20'
                            : 'hover:bg-muted border-border'
                        }`}
                        onClick={() => setSelectedAgent(agent)}
                      >
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <TypeIcon className="h-4 w-4 text-primary" />
                            <span className="font-medium text-sm">{agent.name}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className={`w-2 h-2 rounded-full ${getStatusColor(agent.status)}`} />
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleAgentToggle(agent.id);
                              }}
                            >
                              {agent.status === 'active' ? (
                                <Pause className="h-3 w-3" />
                              ) : (
                                <Play className="h-3 w-3" />
                              )}
                            </Button>
                          </div>
                        </div>
                        
                        <p className="text-xs text-muted-foreground mb-2">
                          {agent.description}
                        </p>
                        
                        <div className="flex items-center justify-between text-xs">
                          <Badge variant="outline" className="text-xs">
                            {agent.type}
                          </Badge>
                          <span className="text-muted-foreground">
                            {agent.connections} connections
                          </span>
                        </div>
                        
                        {agent.lastRun && (
                          <div className="text-xs text-muted-foreground mt-1">
                            Last run: {agent.lastRun.toLocaleTimeString()}
                          </div>
                        )}
                      </motion.div>
                    );
                  })}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>

          {/* Agent Details / Workflow View */}
          <Card className="lg:col-span-2">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg">
                {selectedAgent ? `${selectedAgent.name} Configuration` : 'Agent Workflow Designer'}
              </CardTitle>
            </CardHeader>
            
            <Separator />
            
            <CardContent className="p-0 h-full">
              {selectedAgent ? (
                <div className="p-6">
                  <div className="space-y-6">
                    {/* Agent Info */}
                    <div>
                      <h3 className="font-semibold mb-3">Agent Information</h3>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium">Name</label>
                          <div className="text-sm text-muted-foreground">{selectedAgent.name}</div>
                        </div>
                        <div>
                          <label className="text-sm font-medium">Type</label>
                          <div className="text-sm text-muted-foreground capitalize">{selectedAgent.type}</div>
                        </div>
                        <div>
                          <label className="text-sm font-medium">Status</label>
                          <div className="flex items-center space-x-2">
                            <div className={`w-2 h-2 rounded-full ${getStatusColor(selectedAgent.status)}`} />
                            <span className="text-sm text-muted-foreground capitalize">{selectedAgent.status}</span>
                          </div>
                        </div>
                        <div>
                          <label className="text-sm font-medium">Connections</label>
                          <div className="text-sm text-muted-foreground">{selectedAgent.connections}</div>
                        </div>
                      </div>
                    </div>

                    {/* Configuration */}
                    <div>
                      <h3 className="font-semibold mb-3">Configuration</h3>
                      <div className="space-y-3">
                        <div>
                          <label className="text-sm font-medium">Description</label>
                          <textarea 
                            className="w-full mt-1 p-2 border rounded-md text-sm"
                            value={selectedAgent.description}
                            rows={3}
                            readOnly
                          />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm font-medium">Model</label>
                            <select className="w-full mt-1 p-2 border rounded-md text-sm">
                              <option>GPT-4</option>
                              <option>Claude-3</option>
                              <option>Custom Model</option>
                            </select>
                          </div>
                          <div>
                            <label className="text-sm font-medium">Temperature</label>
                            <input 
                              type="number" 
                              className="w-full mt-1 p-2 border rounded-md text-sm"
                              defaultValue="0.7"
                              min="0"
                              max="1"
                              step="0.1"
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex space-x-2 pt-4 border-t">
                      <Button size="sm">Save Changes</Button>
                      <Button variant="outline" size="sm">Test Agent</Button>
                      <Button variant="outline" size="sm">View Logs</Button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center text-muted-foreground">
                    <Workflow className="h-16 w-16 mx-auto mb-4 opacity-50" />
                    <h3 className="text-lg font-semibold mb-2">Agent Studio</h3>
                    <p className="mb-4 max-w-md">
                      Select an agent from the sidebar to configure it, or create a new agent 
                      to get started with the Jazz framework's agent system.
                    </p>
                    <div className="space-y-2 text-sm">
                      <p>• Configure AI models and parameters</p>
                      <p>• Design custom workflows</p>
                      <p>• Monitor agent performance</p>
                      <p>• Connect to ReelMind backend</p>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Info Panel */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mt-6"
        >
          <Card>
            <CardContent className="p-4">
              <div className="text-sm text-muted-foreground">
                <strong>Agent Studio:</strong> This is the Jazz framework's agent management interface, 
                providing tools for configuring AI agents, designing workflows, and monitoring performance. 
                All agents connect to the ReelMind Python backend for processing and data persistence.
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
