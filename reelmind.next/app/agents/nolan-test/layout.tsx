import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Nolan Test - Jazz Framework Integration',
  description: 'Complete Jazz React frontend integrated into ReelMind with Python backend connectivity',
  keywords: ['Jazz Framework', 'ReelMind', 'AI', 'Canvas', 'Chat', 'Knowledge Base', 'Agent Studio'],
};

export default function NolanTestLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-background">
      {/* Jazz Framework Styles */}
      <style jsx global>{`
        /* Jazz-specific global styles */
        .jazz-container {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        }
        
        .jazz-canvas {
          border-radius: 8px;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .jazz-chat {
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
          backdrop-filter: blur(10px);
        }
        
        .jazz-knowledge {
          border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .jazz-studio {
          background: radial-gradient(circle at 50% 50%, rgba(120, 119, 198, 0.1), transparent);
        }
        
        /* Animation classes for Jazz components */
        .jazz-fade-in {
          animation: jazzFadeIn 0.5s ease-in-out;
        }
        
        @keyframes jazzFadeIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        .jazz-slide-in {
          animation: jazzSlideIn 0.3s ease-out;
        }
        
        @keyframes jazzSlideIn {
          from {
            transform: translateX(-20px);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
        
        /* Jazz component specific styles */
        .excalidraw-container {
          border-radius: 12px;
          overflow: hidden;
        }
        
        .chat-message {
          border-radius: 18px;
          padding: 12px 16px;
          margin: 8px 0;
        }
        
        .knowledge-item {
          transition: all 0.2s ease;
          border-radius: 8px;
        }
        
        .knowledge-item:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .agent-card {
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          transition: all 0.3s ease;
        }
        
        .agent-card:hover {
          background: rgba(255, 255, 255, 0.08);
          border-color: rgba(255, 255, 255, 0.2);
        }
        
        /* Responsive design for Jazz components */
        @media (max-width: 768px) {
          .jazz-container {
            padding: 16px;
          }
          
          .jazz-canvas {
            height: 60vh;
          }
          
          .chat-message {
            max-width: 85%;
          }
        }
        
        /* Dark mode adjustments for Jazz components */
        @media (prefers-color-scheme: dark) {
          .jazz-chat {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.1));
          }
          
          .jazz-knowledge {
            border-color: rgba(255, 255, 255, 0.1);
          }
          
          .agent-card {
            background: rgba(0, 0, 0, 0.2);
            border-color: rgba(255, 255, 255, 0.1);
          }
        }
        
        /* Jazz branding colors */
        .jazz-primary {
          color: #6366f1;
        }
        
        .jazz-secondary {
          color: #8b5cf6;
        }
        
        .jazz-accent {
          color: #06b6d4;
        }
        
        /* Loading states for Jazz components */
        .jazz-loading {
          position: relative;
          overflow: hidden;
        }
        
        .jazz-loading::after {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent
          );
          animation: jazzShimmer 1.5s infinite;
        }
        
        @keyframes jazzShimmer {
          0% {
            left: -100%;
          }
          100% {
            left: 100%;
          }
        }
        
        /* Jazz component focus states */
        .jazz-focusable:focus {
          outline: 2px solid #6366f1;
          outline-offset: 2px;
        }
        
        /* Jazz scrollbar styling */
        .jazz-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        
        .jazz-scrollbar::-webkit-scrollbar-track {
          background: rgba(0, 0, 0, 0.1);
          border-radius: 3px;
        }
        
        .jazz-scrollbar::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.3);
          border-radius: 3px;
        }
        
        .jazz-scrollbar::-webkit-scrollbar-thumb:hover {
          background: rgba(0, 0, 0, 0.5);
        }
      `}</style>
      
      {/* Jazz Framework Provider Context */}
      <div className="jazz-container">
        {children}
      </div>
    </div>
  );
}
