"use client";

import { useState } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  MessageSquare,
  Canvas,
  BookOpen,
  Cog,
  ArrowRight,
  Sparkles
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

const features = [
  {
    title: "Chat Interface",
    description: "Interactive AI chat with advanced conversation capabilities",
    icon: MessageSquare,
    href: "/agents/nolan-test/chat",
    color: "from-blue-500 to-cyan-500",
    delay: 0.1
  },
  {
    title: "Canvas Studio",
    description: "Visual canvas for creative design and collaboration",
    icon: Canvas,
    href: "/agents/nolan-test/canvas",
    color: "from-purple-500 to-pink-500",
    delay: 0.2
  },
  {
    title: "Knowledge Base",
    description: "Comprehensive knowledge management and documentation",
    icon: BookOpen,
    href: "/agents/nolan-test/knowledge",
    color: "from-green-500 to-emerald-500",
    delay: 0.3
  },
  {
    title: "Agent Studio",
    description: "Advanced agent configuration and workflow management",
    icon: Cog,
    href: "/agents/nolan-test/studio",
    color: "from-orange-500 to-red-500",
    delay: 0.4
  }
];

export default function NolanTestPage() {
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-8">
        {/* Header Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="flex items-center justify-center mb-4">
            <Sparkles className="h-8 w-8 text-primary mr-3" />
            <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
              Nolan Test Studio
            </h1>
          </div>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Experience the complete Jazz framework integrated into ReelMind.
            Explore advanced AI capabilities with our comprehensive design agent platform.
          </p>
          <div className="mt-6 inline-flex items-center px-4 py-2 rounded-full bg-primary/10 border border-primary/20">
            <span className="text-sm font-medium text-primary">Beta Version</span>
          </div>
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: feature.delay }}
                onHoverStart={() => setHoveredCard(index)}
                onHoverEnd={() => setHoveredCard(null)}
              >
                <Link href={feature.href}>
                  <Card className="h-full cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105 border-2 hover:border-primary/20">
                    <CardHeader className="pb-4">
                      <div className="flex items-center space-x-3">
                        <div className={`p-3 rounded-lg bg-gradient-to-r ${feature.color} text-white`}>
                          <Icon className="h-6 w-6" />
                        </div>
                        <div className="flex-1">
                          <CardTitle className="text-xl font-semibold">
                            {feature.title}
                          </CardTitle>
                        </div>
                        <motion.div
                          animate={{ x: hoveredCard === index ? 5 : 0 }}
                          transition={{ duration: 0.2 }}
                        >
                          <ArrowRight className="h-5 w-5 text-muted-foreground" />
                        </motion.div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="text-base">
                        {feature.description}
                      </CardDescription>
                    </CardContent>
                  </Card>
                </Link>
              </motion.div>
            );
          })}
        </div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="mt-12 max-w-4xl mx-auto"
        >
          <Card>
            <CardHeader>
              <CardTitle className="text-center">Quick Start</CardTitle>
              <CardDescription className="text-center">
                Get started with Jazz framework features
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2">
                  <MessageSquare className="h-6 w-6" />
                  <span>Start New Chat</span>
                  <span className="text-xs text-muted-foreground">Begin AI conversation</span>
                </Button>
                <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2">
                  <Canvas className="h-6 w-6" />
                  <span>Create Canvas</span>
                  <span className="text-xs text-muted-foreground">Design and collaborate</span>
                </Button>
                <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2">
                  <BookOpen className="h-6 w-6" />
                  <span>Browse Knowledge</span>
                  <span className="text-xs text-muted-foreground">Explore documentation</span>
                </Button>
                <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2">
                  <Cog className="h-6 w-6" />
                  <span>Configure Agent</span>
                  <span className="text-xs text-muted-foreground">Set up automation</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Additional Info Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mt-16 text-center"
        >
          <div className="max-w-3xl mx-auto">
            <h2 className="text-2xl font-semibold mb-4">
              Powered by Jazz Framework
            </h2>
            <p className="text-muted-foreground mb-8">
              This integration brings the complete Jazz React frontend into ReelMind,
              maintaining all original UI designs and interactions while connecting
              seamlessly to the ReelMind Python backend infrastructure.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <div className="px-4 py-2 bg-muted rounded-full">
                <span className="text-sm font-medium">React + TypeScript</span>
              </div>
              <div className="px-4 py-2 bg-muted rounded-full">
                <span className="text-sm font-medium">Excalidraw Canvas</span>
              </div>
              <div className="px-4 py-2 bg-muted rounded-full">
                <span className="text-sm font-medium">AI Chat Interface</span>
              </div>
              <div className="px-4 py-2 bg-muted rounded-full">
                <span className="text-sm font-medium">Python Backend</span>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Status Panel */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.7 }}
          className="mt-8 max-w-4xl mx-auto"
        >
          <Card>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                <div>
                  <div className="flex items-center justify-center mb-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                    <span className="font-medium">Backend Status</span>
                  </div>
                  <p className="text-sm text-muted-foreground">Connected to ReelMind Python Server</p>
                </div>
                <div>
                  <div className="flex items-center justify-center mb-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                    <span className="font-medium">Jazz Integration</span>
                  </div>
                  <p className="text-sm text-muted-foreground">All components loaded successfully</p>
                </div>
                <div>
                  <div className="flex items-center justify-center mb-2">
                    <div className="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                    <span className="font-medium">UI Framework</span>
                  </div>
                  <p className="text-sm text-muted-foreground">Original Jazz design preserved</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
