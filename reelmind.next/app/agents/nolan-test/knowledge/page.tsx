"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  BookOpen, 
  Settings, 
  Search, 
  Plus, 
  FileText,
  Folder,
  Edit,
  Trash2
} from 'lucide-react';
import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';

interface KnowledgeItem {
  id: string;
  title: string;
  type: 'document' | 'folder';
  content?: string;
  lastModified: Date;
  size?: string;
}

export default function NolanTestKnowledgePage() {
  const [knowledgeItems, setKnowledgeItems] = useState<KnowledgeItem[]>([
    {
      id: '1',
      title: 'Getting Started Guide',
      type: 'document',
      content: 'Welcome to the Jazz knowledge base...',
      lastModified: new Date(),
      size: '2.3 KB'
    },
    {
      id: '2',
      title: 'API Documentation',
      type: 'folder',
      lastModified: new Date()
    },
    {
      id: '3',
      title: 'Design Patterns',
      type: 'document',
      content: 'Common design patterns used in the Jazz framework...',
      lastModified: new Date(),
      size: '5.7 KB'
    },
    {
      id: '4',
      title: 'Troubleshooting',
      type: 'document',
      content: 'Common issues and their solutions...',
      lastModified: new Date(),
      size: '3.1 KB'
    }
  ]);
  
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedItem, setSelectedItem] = useState<KnowledgeItem | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  const filteredItems = knowledgeItems.filter(item =>
    item.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleItemSelect = (item: KnowledgeItem) => {
    setSelectedItem(item);
    setIsEditing(false);
  };

  const handleNewDocument = () => {
    const newDoc: KnowledgeItem = {
      id: Date.now().toString(),
      title: 'New Document',
      type: 'document',
      content: '',
      lastModified: new Date(),
      size: '0 KB'
    };
    setKnowledgeItems(prev => [...prev, newDoc]);
    setSelectedItem(newDoc);
    setIsEditing(true);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="flex items-center justify-between mb-6"
        >
          <div className="flex items-center space-x-4">
            <Link href="/agents/nolan-test">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            </Link>
            <div className="flex items-center space-x-2">
              <BookOpen className="h-6 w-6 text-primary" />
              <h1 className="text-2xl font-bold">Knowledge Base</h1>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={handleNewDocument}>
              <Plus className="h-4 w-4 mr-2" />
              New Document
            </Button>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </div>
        </motion.div>

        {/* Main Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]"
        >
          {/* Sidebar - Knowledge Items List */}
          <Card className="lg:col-span-1">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg">Documents</CardTitle>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search knowledge base..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </CardHeader>
            
            <Separator />
            
            <CardContent className="p-0">
              <ScrollArea className="h-[calc(100vh-350px)]">
                <div className="p-4 space-y-2">
                  {filteredItems.map((item) => (
                    <motion.div
                      key={item.id}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3 }}
                      className={`p-3 rounded-lg cursor-pointer transition-colors ${
                        selectedItem?.id === item.id
                          ? 'bg-primary/10 border border-primary/20'
                          : 'hover:bg-muted'
                      }`}
                      onClick={() => handleItemSelect(item)}
                    >
                      <div className="flex items-center space-x-3">
                        {item.type === 'folder' ? (
                          <Folder className="h-4 w-4 text-blue-500" />
                        ) : (
                          <FileText className="h-4 w-4 text-green-500" />
                        )}
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-sm truncate">
                            {item.title}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {item.lastModified.toLocaleDateString()}
                            {item.size && ` • ${item.size}`}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>

          {/* Main Content Area */}
          <Card className="lg:col-span-2">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">
                  {selectedItem ? selectedItem.title : 'Select a document'}
                </CardTitle>
                {selectedItem && (
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsEditing(!isEditing)}
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      {isEditing ? 'Preview' : 'Edit'}
                    </Button>
                    <Button variant="outline" size="sm">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>
            </CardHeader>
            
            <Separator />
            
            <CardContent className="p-0 h-full">
              {selectedItem ? (
                <div className="h-full">
                  {isEditing ? (
                    <textarea
                      className="w-full h-full p-4 border-none resize-none focus:outline-none"
                      value={selectedItem.content || ''}
                      onChange={(e) => {
                        setSelectedItem(prev => prev ? {
                          ...prev,
                          content: e.target.value
                        } : null);
                      }}
                      placeholder="Start writing your document..."
                    />
                  ) : (
                    <ScrollArea className="h-full">
                      <div className="p-4">
                        {selectedItem.content ? (
                          <div className="prose prose-sm max-w-none">
                            <pre className="whitespace-pre-wrap font-sans">
                              {selectedItem.content}
                            </pre>
                          </div>
                        ) : (
                          <div className="text-center text-muted-foreground py-12">
                            <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                            <p>This document is empty.</p>
                            <Button
                              variant="outline"
                              size="sm"
                              className="mt-4"
                              onClick={() => setIsEditing(true)}
                            >
                              Start editing
                            </Button>
                          </div>
                        )}
                      </div>
                    </ScrollArea>
                  )}
                </div>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center text-muted-foreground">
                    <BookOpen className="h-16 w-16 mx-auto mb-4 opacity-50" />
                    <h3 className="text-lg font-semibold mb-2">Knowledge Base</h3>
                    <p className="mb-4 max-w-md">
                      Select a document from the sidebar to view or edit it. 
                      This is the Jazz framework's knowledge management system.
                    </p>
                    <Button onClick={handleNewDocument}>
                      <Plus className="h-4 w-4 mr-2" />
                      Create New Document
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Info Panel */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mt-6"
        >
          <Card>
            <CardContent className="p-4">
              <div className="text-sm text-muted-foreground">
                <strong>Knowledge Management:</strong> This knowledge base interface maintains 
                the original Jazz framework design and functionality. Documents are stored 
                and synchronized with the ReelMind Python backend for persistence and search capabilities.
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
