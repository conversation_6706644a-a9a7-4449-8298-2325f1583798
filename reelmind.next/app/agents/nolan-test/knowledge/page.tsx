"use client";

import { useState } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, BookOpen } from 'lucide-react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { JazzKnowledgeBase } from '@/components/jazz/components/JazzKnowledgeBase';

interface KnowledgeItem {
  id: string;
  title: string;
  content: string;
  type: 'document' | 'folder' | 'note' | 'reference';
  tags: string[];
  author: string;
  createdAt: Date;
  updatedAt: Date;
  size: number;
  parentId?: string;
  isPublic: boolean;
  version: number;
}

export default function NolanTestKnowledgePage() {
  const [initialItems] = useState<KnowledgeItem[]>([
    {
      id: '1',
      title: 'Getting Started Guide',
      content: 'Welcome to the Jazz knowledge base! This comprehensive guide will help you understand how to use the Jazz framework integrated with ReelMind.\n\nThe Jazz framework provides:\n- Interactive chat interfaces\n- Canvas-based design tools\n- Knowledge management systems\n- Agent configuration studios\n\nAll components are seamlessly integrated with the ReelMind Python backend for powerful AI capabilities.',
      type: 'document',
      tags: ['guide', 'getting-started', 'jazz', 'reelmind'],
      author: 'Jazz Team',
      createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(),
      size: 512,
      isPublic: true,
      version: 3
    },
    {
      id: '2',
      title: 'API Documentation',
      content: 'Complete API documentation for Jazz framework integration with ReelMind backend.\n\nEndpoints:\n- /api/chat - Chat interface\n- /api/canvas - Canvas operations\n- /api/knowledge - Knowledge base\n- /api/agents - Agent management\n\nAuthentication: Bearer token required for all endpoints.',
      type: 'reference',
      tags: ['api', 'documentation', 'backend', 'endpoints'],
      author: 'Development Team',
      createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      size: 1024,
      isPublic: true,
      version: 5
    },
    {
      id: '3',
      title: 'Design Patterns',
      content: 'Common design patterns used in the Jazz framework for building scalable and maintainable applications.\n\n1. Component Composition\n2. State Management with Stores\n3. API Adapter Pattern\n4. Event-Driven Architecture\n5. Responsive Design Principles\n\nEach pattern includes examples and best practices.',
      type: 'document',
      tags: ['design', 'patterns', 'architecture', 'best-practices'],
      author: 'Architecture Team',
      createdAt: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      size: 2048,
      isPublic: true,
      version: 2
    },
    {
      id: '4',
      title: 'Troubleshooting Guide',
      content: 'Common issues and their solutions when working with Jazz framework and ReelMind integration.\n\nCommon Issues:\n- Connection timeouts\n- Component rendering errors\n- State synchronization problems\n- API authentication failures\n\nFor each issue, we provide step-by-step solutions and prevention strategies.',
      type: 'document',
      tags: ['troubleshooting', 'debugging', 'support', 'issues'],
      author: 'Support Team',
      createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      size: 1536,
      isPublic: true,
      version: 4
    }
  ]);

  const handleItemSelect = (item: KnowledgeItem) => {
    console.log('Selected knowledge item:', item.title);
  };

  const handleItemCreate = (item: Partial<KnowledgeItem>) => {
    console.log('Creating new knowledge item:', item.title);
  };

  const handleItemUpdate = (id: string, updates: Partial<KnowledgeItem>) => {
    console.log('Updating knowledge item:', id, updates);
  };

  const handleItemDelete = (id: string) => {
    console.log('Deleting knowledge item:', id);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="flex items-center justify-between mb-6"
        >
          <div className="flex items-center space-x-4">
            <Link href="/agents/nolan-test">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            </Link>
            <div className="flex items-center space-x-2">
              <BookOpen className="h-6 w-6 text-primary" />
              <h1 className="text-2xl font-bold">Knowledge Base</h1>
            </div>
          </div>
        </motion.div>

        {/* Jazz Knowledge Base */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="h-[calc(100vh-200px)]"
        >
          <JazzKnowledgeBase
            initialItems={initialItems}
            onItemSelect={handleItemSelect}
            onItemCreate={handleItemCreate}
            onItemUpdate={handleItemUpdate}
            onItemDelete={handleItemDelete}
            searchable={true}
            editable={true}
            className="h-full"
          />
        </motion.div>
      </div>
    </div>
  );
}
          {/* Sidebar - Knowledge Items List */}
          <Card className="lg:col-span-1">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg">Documents</CardTitle>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search knowledge base..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </CardHeader>

            <Separator />

            <CardContent className="p-0">
              <ScrollArea className="h-[calc(100vh-350px)]">
                <div className="p-4 space-y-2">
                  {filteredItems.map((item) => (
                    <motion.div
                      key={item.id}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3 }}
                      className={`p-3 rounded-lg cursor-pointer transition-colors ${
                        selectedItem?.id === item.id
                          ? 'bg-primary/10 border border-primary/20'
                          : 'hover:bg-muted'
                      }`}
                      onClick={() => handleItemSelect(item)}
                    >
                      <div className="flex items-center space-x-3">
                        {item.type === 'folder' ? (
                          <Folder className="h-4 w-4 text-blue-500" />
                        ) : (
                          <FileText className="h-4 w-4 text-green-500" />
                        )}
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-sm truncate">
                            {item.title}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {item.lastModified.toLocaleDateString()}
                            {item.size && ` • ${item.size}`}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>

          {/* Main Content Area */}
          <Card className="lg:col-span-2">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">
                  {selectedItem ? selectedItem.title : 'Select a document'}
                </CardTitle>
                {selectedItem && (
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsEditing(!isEditing)}
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      {isEditing ? 'Preview' : 'Edit'}
                    </Button>
                    <Button variant="outline" size="sm">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>
            </CardHeader>

            <Separator />

            <CardContent className="p-0 h-full">
              {selectedItem ? (
                <div className="h-full">
                  {isEditing ? (
                    <textarea
                      className="w-full h-full p-4 border-none resize-none focus:outline-none"
                      value={selectedItem.content || ''}
                      onChange={(e) => {
                        setSelectedItem(prev => prev ? {
                          ...prev,
                          content: e.target.value
                        } : null);
                      }}
                      placeholder="Start writing your document..."
                    />
                  ) : (
                    <ScrollArea className="h-full">
                      <div className="p-4">
                        {selectedItem.content ? (
                          <div className="prose prose-sm max-w-none">
                            <pre className="whitespace-pre-wrap font-sans">
                              {selectedItem.content}
                            </pre>
                          </div>
                        ) : (
                          <div className="text-center text-muted-foreground py-12">
                            <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                            <p>This document is empty.</p>
                            <Button
                              variant="outline"
                              size="sm"
                              className="mt-4"
                              onClick={() => setIsEditing(true)}
                            >
                              Start editing
                            </Button>
                          </div>
                        )}
                      </div>
                    </ScrollArea>
                  )}
                </div>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center text-muted-foreground">
                    <BookOpen className="h-16 w-16 mx-auto mb-4 opacity-50" />
                    <h3 className="text-lg font-semibold mb-2">Knowledge Base</h3>
                    <p className="mb-4 max-w-md">
                      Select a document from the sidebar to view or edit it.
                      This is the Jazz framework's knowledge management system.
                    </p>
                    <Button onClick={handleNewDocument}>
                      <Plus className="h-4 w-4 mr-2" />
                      Create New Document
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Info Panel */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mt-6"
        >
          <Card>
            <CardContent className="p-4">
              <div className="text-sm text-muted-foreground">
                <strong>Knowledge Management:</strong> This knowledge base interface maintains
                the original Jazz framework design and functionality. Documents are stored
                and synchronized with the ReelMind Python backend for persistence and search capabilities.
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
