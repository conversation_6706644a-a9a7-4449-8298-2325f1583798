"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>vas, Settings, Palette, Save, Download } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

// Note: In the full implementation, we would import the actual Excalidraw component
// import { Excalidraw } from '@excalidraw/excalidraw';

export default function NolanTestCanvasPage() {
  const [canvasData, setCanvasData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading canvas data
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  }, []);

  const handleSave = () => {
    // Implement save functionality
    console.log('Saving canvas...');
  };

  const handleExport = () => {
    // Implement export functionality
    console.log('Exporting canvas...');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="flex items-center justify-between mb-6"
        >
          <div className="flex items-center space-x-4">
            <Link href="/agents/nolan-test">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            </Link>
            <div className="flex items-center space-x-2">
              <Canvas className="h-6 w-6 text-primary" />
              <h1 className="text-2xl font-bold">Canvas Studio</h1>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={handleSave}>
              <Save className="h-4 w-4 mr-2" />
              Save
            </Button>
            <Button variant="outline" size="sm" onClick={handleExport}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </div>
        </motion.div>

        {/* Canvas Container */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="max-w-full mx-auto"
        >
          <Card className="h-[calc(100vh-200px)]">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center space-x-2">
                <Palette className="h-5 w-5 text-primary" />
                <span>Excalidraw Canvas</span>
              </CardTitle>
              <div className="text-sm text-muted-foreground">
                Visual design and collaboration workspace
              </div>
            </CardHeader>
            
            <Separator />
            
            {/* Canvas Area */}
            <CardContent className="p-0 h-full">
              {isLoading ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                    <p className="text-muted-foreground">Loading canvas...</p>
                  </div>
                </div>
              ) : (
                <div className="h-full w-full relative">
                  {/* Placeholder for Excalidraw component */}
                  <div className="h-full w-full bg-muted/20 flex items-center justify-center border-2 border-dashed border-muted-foreground/20 rounded-lg m-4">
                    <div className="text-center">
                      <Canvas className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">Excalidraw Canvas</h3>
                      <p className="text-muted-foreground mb-4 max-w-md">
                        This is where the Jazz framework's Excalidraw canvas component would be rendered. 
                        The original canvas functionality from Jazz is preserved here.
                      </p>
                      <div className="space-y-2 text-sm text-muted-foreground">
                        <p>• Draw and sketch with various tools</p>
                        <p>• Collaborate in real-time</p>
                        <p>• Export to multiple formats</p>
                        <p>• Integrate with AI chat</p>
                      </div>
                    </div>
                  </div>
                  
                  {/* In the actual implementation, this would be: */}
                  {/* 
                  <Excalidraw
                    initialData={canvasData}
                    onChange={(elements, appState) => {
                      // Handle canvas changes
                    }}
                    UIOptions={{
                      canvasActions: {
                        loadScene: false,
                        saveToActiveFile: false,
                        export: false,
                        toggleTheme: false
                      }
                    }}
                  />
                  */}
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Toolbar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mt-6 max-w-full mx-auto"
        >
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="text-sm font-medium">Tools:</div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm">Pen</Button>
                    <Button variant="outline" size="sm">Rectangle</Button>
                    <Button variant="outline" size="sm">Circle</Button>
                    <Button variant="outline" size="sm">Arrow</Button>
                    <Button variant="outline" size="sm">Text</Button>
                  </div>
                </div>
                <div className="text-sm text-muted-foreground">
                  Jazz Canvas • Connected to ReelMind Backend
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Info Panel */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mt-6 max-w-full mx-auto"
        >
          <Card>
            <CardContent className="p-4">
              <div className="text-sm text-muted-foreground">
                <strong>Canvas Integration:</strong> This canvas interface uses the same Excalidraw component 
                from the Jazz framework, maintaining all original drawing capabilities and UI interactions. 
                Canvas data is synchronized with the ReelMind Python backend for persistence and collaboration.
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
