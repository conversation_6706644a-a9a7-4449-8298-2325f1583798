"use client";

import { useState } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Paintbrush2 } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { JazzCanvasStudio } from '@/components/jazz/components/JazzCanvasStudio';

export default function NolanTestCanvasPage() {
  const [canvasId] = useState('jazz-canvas-demo');

  const handleSave = (elements: any[]) => {
    console.log('Canvas saved with elements:', elements.length);
  };

  const handleExport = (format: 'png' | 'svg' | 'json') => {
    console.log('Exporting canvas as:', format);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="flex items-center justify-between mb-6"
        >
          <div className="flex items-center space-x-4">
            <Link href="/agents/nolan-test">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            </Link>
            <div className="flex items-center space-x-2">
              <Paintbrush2 className="h-6 w-6 text-primary" />
              <h1 className="text-2xl font-bold">Canvas Studio</h1>
            </div>
          </div>
        </motion.div>

        {/* Jazz Canvas Studio */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="max-w-full mx-auto h-[calc(100vh-200px)]"
        >
          <JazzCanvasStudio
            canvasId={canvasId}
            onSave={handleSave}
            onExport={handleExport}
            collaborative={true}
            className="h-full"
          />
        </motion.div>


      </div>
    </div>
  );
}
