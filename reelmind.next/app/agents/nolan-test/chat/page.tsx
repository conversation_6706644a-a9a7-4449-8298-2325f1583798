"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON>, MessageSquare, Settings, Sparkles } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { JazzChatInterface } from '@/components/jazz/components/JazzChatInterface';

interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  metadata?: {
    model?: string;
    tokens?: number;
    processingTime?: number;
  };
}

export default function NolanTestChatPage() {
  const [initialMessages] = useState<Message[]>([
    {
      id: '1',
      content: 'Welcome to Nolan Test Chat! This is the Jazz framework chat interface integrated into ReelMind. I can help you with various tasks including image generation, canvas design, and knowledge management.',
      role: 'assistant',
      timestamp: new Date(),
      metadata: {
        model: 'jazz-ai',
        tokens: 45,
        processingTime: 120
      }
    }
  ]);

  const handleMessage = (message: Message) => {
    console.log('New message received:', message);
    // Handle message events from the Jazz chat interface
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="flex items-center justify-between mb-6"
        >
          <div className="flex items-center space-x-4">
            <Link href="/agents/nolan-test">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            </Link>
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-6 w-6 text-primary" />
              <h1 className="text-2xl font-bold">Chat Interface</h1>
            </div>
          </div>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </motion.div>

        {/* Jazz Chat Interface */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="max-w-6xl mx-auto h-[calc(100vh-200px)]"
        >
          <JazzChatInterface
            initialMessages={initialMessages}
            onMessage={handleMessage}
            placeholder="Ask me anything about design, canvas creation, or knowledge management..."
            showMetadata={true}
            className="h-full"
          />
        </motion.div>

        {/* Info Panel */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mt-6 max-w-4xl mx-auto"
        >
          <Card>
            <CardContent className="p-4">
              <div className="text-sm text-muted-foreground">
                <strong>Note:</strong> This is the Jazz framework chat interface integrated into ReelMind.
                The UI and interactions are preserved from the original Jazz project,
                while the backend connects to the ReelMind Python server for AI processing.
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
